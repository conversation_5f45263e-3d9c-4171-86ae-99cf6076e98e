from typing import Dict

from yunfu.common import LogUtils
from yunfu.db.graph.core.models.config import Config
from yunfu.db.graph.graph.graph_dbs import Neo4jGraphDb
from yunfu.db.graph.models import Node

from .base_graph_mapper import BaseGraphMapper

logger = LogUtils.get_logger(__name__)


class Neo4jGraphMapper(BaseGraphMapper):
    graph_db: Neo4jGraphDb

    def __init__(self, db_config: dict, enable_version: bool = True):
        self.graph_db = Neo4jGraphDb(
            Config(db=db_config, version={"enabled": enable_version})   # type: ignore
        )

    def get_node_by_eid(self, space: str, eid: str) -> Node:
        graph = self.graph_db.get_graph(space)
        node = graph.nodes.match(props=[("_eid", "=", eid)]).first()
        if not node:
            raise ValueError("实体不存在")
        return node

    def get_max_relation_node_with_labels(
        self, space: str, version: str, show_ontology: bool = True
    ):
        graph = self.graph_db.get_graph(space)
        labels = [space, version]
        if show_ontology:
            query = f"""MATCH (n:{':'.join(labels)})-[r]-(m:{':'.join(labels)}) WHERE n:concept
            AND n.name<>"事物"
            RETURN n, count(r) as rel_count ORDER BY rel_count DESC LIMIT 10
            """
        else:
            query = f"""MATCH (n:{':'.join(labels)})-[r]-(m:{':'.join(labels)}) WHERE not n:concept
            AND n.name<>"事物"
            RETURN n, count(r) as rel_count ORDER BY rel_count DESC LIMIT 10
            """
        return graph.data_wrapper.as_nodes(graph.client.run(query).data)

    def get_single_node_with_labels(
        self, space: str, version: str, show_ontology: bool = True
    ):
        graph = self.graph_db.get_graph(space)
        labels = [space, version]
        if show_ontology:
            query = f"""MATCH (n:{':'.join(labels)}) where n:concept RETURN n LIMIT 1"""
        else:
            query = (
                f"""MATCH (n:{':'.join(labels)}) where not n:concept RETURN n LIMIT 1"""
            )
        return graph.data_wrapper.as_nodes(graph.client.run(query).data)

    def count_kg(self, space: str):
        graph = self.graph_db.get_graph(space)
        count_total = {
            "relations": 0,
            # 'ontologies': self.count_kg_ontologies(label),
            "properties": 0,
        }
        results: Dict[str, dict] = {
            "entities": {},
            "relations": {},
            "properties": {},
        }
        query = (
            """MATCH (n:c:`{0}`)-[r]->(m:c:`{0}`) WHERE NOT (n:concept) AND r.c=true """
            """RETURN DISTINCT(r.name) AS name, COUNT(r.name) AS count"""
        ).format(space)
        logger.info(f"first query: {query}")
        data = list(graph.client.run(query).data)
        if data:
            logger.info(f"first query result: {data}")
            for item in data:
                results["relations"][item["name"]] = item["count"]
                count_total["relations"] += item["count"]
        label_category = "concept"
        query = (
            f"MATCH (n:c:{space})-[:`属于`*1..5]->(m:c:{space}:{label_category})-"
            f"[:`属于`]->(k:c:{space}:{label_category}) WHERE n.name <> m.name "
            f'AND not n:{label_category}  AND m.name <> "事物"'
            "RETURN DISTINCT m.name AS name, COUNT(DISTINCT n._eid) AS count, id(m) as id;"
        )
        data = list(graph.client.run(query).data)
        if not data:
            query = (
                f"MATCH(n:c:`{space}`) where not (n:c:`concept`) "
                "RETURN DISTINCT(n._type) AS name, COUNT(n._type) AS count"
            )
            data = list(graph.client.run(query).data)
        entity_count = self.count_kg_entities(space)
        count_total["entities"] = entity_count
        if data:
            count = 0
            for item in data:
                results["entities"][item["name"]] = item["count"]
                count += item["count"]
            if count < entity_count:
                results["entities"]["其他"] = entity_count - count
        properties_count = self.count_kg_properties(space)
        for item in properties_count:
            count_total["properties"] += item["count(*)"]
            results["properties"][item["key"]] = item["count(*)"]
        return results, count_total

    def count_ontology(self, space: str):
        graph = self.graph_db.get_graph(space)
        count_total = {"relations": 0, "properties": 0}
        results: Dict[str, dict] = {
            "entities": {},
            "relations": {},
            "properties": {},
        }
        query = (
            """MATCH (n:concept:c:`{0}`)-[r]->(m:concept:c:`{0}`) WHERE m.name <> '事物' AND r.c=true """
            """RETURN DISTINCT(r.name) AS name, COUNT(r.name) AS count"""
        ).format(space)
        logger.info(f"first query: {query}")
        data = list(graph.client.run(query).data)
        if data:
            logger.info(f"first query result: {data}")
            for item in data:
                results["relations"][item["name"]] = item["count"]
                count_total["relations"] += item["count"]
        label_category = "concept"
        query = (
            f"MATCH (n:concept:c:{space})-[:`属于`*1..5]->(m:concept:c:{space}:{label_category})-"
            f"[:`属于`]->(k:c:{space}:{label_category}) WHERE n.name <> m.name "
            f"AND not n:{label_category} "
            "RETURN DISTINCT m.name AS name, COUNT(DISTINCT n._eid) AS count, id(m) as id;"
        )

        data = list(graph.client.run(query).data)
        if not data:
            query = (
                f"MATCH(n:c:concept:`{space}`) where n.name<>'事物' "
                "RETURN DISTINCT(n._type) AS name, COUNT(n._type) AS count"
            )
            data = list(graph.client.run(query).data)
        entity_count = self.count_kg_ontologies(space)
        count_total["entities"] = entity_count
        if data:
            count = 0
            for item in data:
                results["entities"][item["name"]] = item["count"]
                count += item["count"]
            if count < entity_count:
                results["entities"]["其他"] = entity_count - count
        properties_count = self.count_ontology_properties(space)
        for item in properties_count:
            count_total["properties"] += item["count(*)"]
            results["properties"][item["key"]] = item["count(*)"]
        return results, count_total

    def count_kg_entities(self, space: str):
        """统计图谱所有实体的总数"""
        graph = self.graph_db.get_graph(space)
        query = (
            f"""MATCH(n:c:`{space}`) where not (n:`concept`) return count(n) as count"""
        )
        result = list(graph.client.run(query).data)
        return result[0]["count"] if result else 0

    def count_kg_properties(self, space: str):
        """统计图谱所有节点的属性总数"""
        graph = self.graph_db.get_graph(space)
        query = (
            f"MATCH(n:c:`{space}`) WHERE NOT (n:c:`concept`) UNWIND KEYS(n) AS key WITH key"
            " WHERE NOT key STARTS WITH '_'"
            " AND NOT key CONTAINS '@'"
            " RETURN key, count(*)"
        )
        return list(graph.client.run(query).data)

    def count_kg_ontologies(self, space: str):
        """统计图谱所有本体的总数"""
        graph = self.graph_db.get_graph(space)
        query = f"""MATCH(n:c:`{space}`:`concept`) where n.name <> '事物' return count(n) as count"""
        result = list(graph.client.run(query).data)
        return result[0]["count"] if result else 0

    def count_ontology_properties(self, space: str):
        """统计图谱所有本体的属性总数"""
        graph = self.graph_db.get_graph(space)
        query = (
            f"MATCH(n:c:concept:`{space}`) WHERE n.name<>'事物' UNWIND KEYS(n) AS key WITH key"
            " WHERE NOT key IN ['_eid', '_show_name', '_create_time', '_update_time', '_type']"
            " RETURN key, count(*)"
        )
        return list(graph.client.run(query).data)
