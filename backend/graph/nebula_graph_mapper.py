from typing import Dict, List, Union

from yunfu.common import LogUtils
from yunfu.db.graph.core.models.config import Config
from yunfu.db.graph.graph.graph_dbs import NebulaGraphDb
from yunfu.db.graph.models import Edge, Node

from .base_graph_mapper import BaseGraphMapper

logger = LogUtils.get_logger(__name__)


class NebulaGraphMapper(BaseGraphMapper):

    graph_db: NebulaGraphDb
    # Nebula 暂时不支持下划线前缀，所以记录下带下划线的属性
    node_private_props = ["show_name", "type", "eid", "create_time", "update_time"]
    edge_private_props = ["rid", "create_time", "update_time"]

    def __init__(self, db_config: dict, enable_version: bool = True):
        self.graph_db = NebulaGraphDb(
            Config(db=db_config, version={"enabled": enable_version})   # type: ignore
        )

    def get_node_by_eid(self, space: str, eid: str) -> Node:
        graph = self.graph_db.get_graph(space)
        node = graph.nodes.match(props=[("eid", "=", eid)]).first()
        if not node:
            raise ValueError("实体不存在")
        return self._restore_node_props(node)

    def get_max_relation_node_with_labels(
        self, space: str, version: str, show_ontology: bool = True
    ):
        graph = self.graph_db.get_graph(space)
        labels = [space, version]
        if show_ontology:
            labels.append("concept")
            query = f"""MATCH (v:{':'.join(labels)})-[r]-(m:{':'.join(labels)}) WHERE v.{space}.name <> "事物"
            RETURN v, count(r) as rel_count ORDER BY rel_count DESC LIMIT 10
            """
        else:
            query = (
                f"MATCH (v:{':'.join(labels)})-[r]-(m:{':'.join(labels)}) "
                f"WITH v, r, labels(v) AS labels WHERE NOT 'concept' IN labels AND v.{space}.name <> '事物' "
                f"RETURN v, count(r) as rel_count ORDER BY rel_count DESC LIMIT 10"
            )
        nodes = graph.data_wrapper.as_nodes(graph.client.run(query).data)
        return [self._restore_node_props(node) for node in nodes]

    def get_single_node_with_labels(
        self, space: str, version: str, show_ontology: bool = True
    ):
        graph = self.graph_db.get_graph(space)
        labels = [space, version]
        if show_ontology:
            labels.append("concept")
            query = f"MATCH (v:{':'.join(labels)}) RETURN v LIMIT 1"
        else:
            query = (
                f"MATCH (v:{':'.join(labels)}) WITH v, labels(v) AS labels "
                "WHERE NOT 'concept' IN labels RETURN v LIMIT 1"
            )
        nodes = graph.data_wrapper.as_nodes(graph.client.run(query).data)
        return [self._restore_node_props(node) for node in nodes]

    def count_kg(self, space: str):
        graph = self.graph_db.get_graph(space)
        count_total = {
            "relations": 0,
            "properties": 0,
        }
        results: Dict[str, dict] = {
            "entities": {},
            "relations": {},
            "properties": {},
        }
        query = (
            f"MATCH (n:c:`{space}`)-[r]->(m:c:`{space}`) WITH n, r, labels(n) AS labels "
            "WHERE NOT 'concept' IN labels AND r.c == true "
            "RETURN DISTINCT(r.name) AS name, COUNT(r.name) AS count"
        )
        rows = graph.data_wrapper.as_rows(graph.client.run(query).data)
        for row in rows:
            results["relations"][row[0]] = row[1]
            count_total["relations"] += row[1]
        query = (
            f"MATCH (n:c:{space})-[:`属于`*1..5]->(m:c:{space}:concept)-"
            f"[:`属于`]->(k:c:{space}:concept)  WITH n, m, labels(n) AS labels WHERE n.{space}.name <> m.{space}.name "
            f"AND NOT 'concept' IN labels AND m.{space}.name <> '事物'"
            f"RETURN DISTINCT m.{space}.name AS name, COUNT(DISTINCT n.{space}.eid) AS count, id(m) as id;"
        )
        data = graph.data_wrapper.as_rows(graph.client.run(query).data)
        if not data:
            query = (
                f"MATCH(n:c:{space}) WITH n, labels(n) AS labels WHERE NOT 'concept' IN labels "
                f"RETURN DISTINCT(n.{space}.type) AS name, COUNT(n.{space}.type) AS count"
            )
            data = graph.data_wrapper.as_rows(graph.client.run(query).data)
        entity_count = self.count_kg_entities(space)
        count_total["entities"] = entity_count
        if data:
            count = 0
            for item in data:
                results["entities"][item[0]] = item[1]
                count += item[1]
            if count < entity_count:
                results["entities"]["其他"] = entity_count - count
        properties_count = self.count_kg_properties(space)
        for item in properties_count:
            count_total["properties"] += item[1]
            results["properties"][item[0]] = item[1]
        return results, count_total

    def count_ontology(self, space: str):
        graph = self.graph_db.get_graph(space)
        count_total = {"relations": 0, "properties": 0}
        results: Dict[str, dict] = {
            "entities": {},
            "relations": {},
            "properties": {},
        }
        query = (
            f"MATCH (n:concept:c:{space})-[r]->(m:concept:c:{space}) WHERE m.{space}.name <> '事物' AND r.c == true "
            "RETURN DISTINCT(r.name) AS name, COUNT(r.name) AS count"
        )
        rows = graph.data_wrapper.as_rows(graph.client.run(query).data)
        for row in rows:
            results["relations"][row[0]] = row[1]
            count_total["relations"] += row[1]
        label_category = "concept"
        query = (
            f"MATCH (n:c:{space})-[r1*1..5]->(m:concept:c:{space}:{label_category})-"
            f"[r2]->(k:c:{space}:{label_category}) WHERE n.{space}.name <> m.{space}.name "
            f"AND r1.name == '属于' AND r2.name == '属于' "
            f"RETURN DISTINCT m.{space}.name AS name, COUNT(DISTINCT n.{space}.eid) AS count"
        )

        data = graph.data_wrapper.as_rows(graph.client.run(query).data)
        if not data:
            query = (
                f"MATCH(n:c:concept:{space}) where n.{space}.name <> '事物' "
                f"RETURN DISTINCT(n.{space}.type) AS name, COUNT(n.{space}.type) AS count"
            )
            data = graph.data_wrapper.as_rows(graph.client.run(query).data)
        entity_count = self.count_kg_ontologies(space)
        count_total["entities"] = entity_count
        if data:
            count = 0
            for item in data:
                results["entities"][item[0]] = item[1]
                count += item[1]
            if count < entity_count:
                results["entities"]["其他"] = entity_count - count
        properties_count = self.count_ontology_properties(space)
        for item in properties_count:
            count_total["properties"] += item[1]
            results["properties"][item[0]] = item[1]
        return results, count_total

    def count_kg_entities(self, space: str):
        """统计图谱所有实体的总数"""
        graph = self.graph_db.get_graph(space)
        query = (
            f"MATCH(n:c:`{space}`) WITH n, labels(n) AS labels "
            "WHERE NOT 'concept' IN labels return count(n) as count"
        )
        result = graph.data_wrapper.as_dict(graph.client.run(query).data)
        return result["count"]

    def count_kg_properties(self, space: str):
        """统计图谱所有节点的属性总数"""
        graph = self.graph_db.get_graph(space)
        query = (
            f"MATCH(n:c:`{space}`) WITH n, labels(n) AS labels"
            " WHERE NOT 'concept' IN labels UNWIND KEYS(n) AS key WITH key"
            " WHERE NOT key IN ['eid', 'show_name', 'create_time', 'update_time', 'type']"
            " AND NOT key CONTAINS '@'"
            " RETURN key, count(*)"
        )
        return graph.data_wrapper.as_rows(graph.client.run(query).data)

    def count_kg_ontologies(self, space: str):
        """统计图谱所有本体的总数"""
        graph = self.graph_db.get_graph(space)
        query = f"MATCH(n:c:{space}:concept) where n.{space}.name <> '事物' return count(n) as count"
        result = graph.data_wrapper.as_dict(graph.client.run(query).data)
        return result["count"]

    def count_ontology_properties(self, space: str):
        """统计图谱所有本体的属性总数"""
        graph = self.graph_db.get_graph(space)
        query = (
            f"MATCH(n:c:concept:{space}) WHERE n.{space}.name<>'事物' UNWIND KEYS(n) AS key WITH key"
            " WHERE NOT key IN ['eid', 'show_name', 'create_time', 'update_time', 'type']"
            " RETURN key, count(*)"
        )
        return graph.data_wrapper.as_rows(graph.client.run(query).data)

    def _restore_props(
        self, node_or_edge: Union[Node, Edge], private_props: List[str]
    ) -> Union[Node, Edge]:
        props = {}
        for key, value in node_or_edge.props.items():
            if key in private_props:
                props[f"_{key}"] = value
            else:
                props[key] = value
        node_or_edge.props = props
        return node_or_edge

    def _restore_node_props(self, node: Node) -> Node:
        return self._restore_props(node, self.node_private_props)

    def _restore_edge_props(self, edge: Edge) -> Edge:
        return self._restore_props(edge, self.edge_private_props)
